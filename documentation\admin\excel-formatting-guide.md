# 📊 Excel/Google Sheets Formatting Guide for Services CSV

## 🎨 Color Coding System

When editing the services CSV in Excel or Google Sheets, apply these colors to make editing safer and more intuitive:

### Color Scheme

| Color | Hex Code | Meaning | Action Required |
|-------|----------|---------|-----------------|
| 🔴 **Red** | `#FF6B6B` | Required Fields | Must not be empty |
| 🟡 **Yellow** | `#FFE66D` | ID Fields | Do NOT modify for existing records |
| 🟢 **Green** | `#4ECDC4` | Editable Fields | Safe to modify |
| 🔵 **Blue** | `#A8E6CF` | New Record Fields | Can be blank for new items |

## 📋 Column Color Coding Reference

### 🔴 RED COLUMNS (Required Fields)
Apply red background to these columns:
- `service_name` - Required for all rows
- `base_duration` - Required for new services  
- `base_price` - Required for new services
- `tier_duration` - Required for pricing tiers
- `tier_price` - Required for pricing tiers

### 🟡 YELLOW COLUMNS (ID Fields - Do Not Modify)
Apply yellow background to these columns:
- `service_id` - Only leave blank for NEW services
- `tier_id` - Only leave blank for NEW tiers
- `category_id` - Only modify if changing category

### 🟢 GREEN COLUMNS (Safe to Edit)
Apply green background to these columns:
- `service_description`
- `tier_name`
- `tier_description`
- `service_color`
- `service_status`
- `service_featured`
- `tier_is_default`
- `tier_sort_order`
- `category_name`
- `visible_public`
- `visible_pos`
- `visible_events`

### 🔵 BLUE COLUMNS (Optional/New Record Fields)
Apply light blue background to these columns:
- `image_url`
- `meta_title`
- `meta_description`
- `booking_requirements`
- `availability_notes`

## 🛠️ Excel Formatting Instructions

### Step 1: Open CSV in Excel
1. Open Excel
2. Go to Data → From Text/CSV
3. Select your services export CSV file
4. Choose "Delimited" and "Comma" as separator
5. Click "Load"

### Step 2: Apply Color Coding
1. Select the entire data range (Ctrl+A)
2. For each column type:
   - Select the entire column (click column header)
   - Right-click → Format Cells → Fill
   - Choose the appropriate color from the scheme above

### Step 3: Add Data Validation
1. **For Boolean Fields** (service_featured, tier_is_default, visible_*):
   - Select the column
   - Data → Data Validation
   - Allow: List
   - Source: `true,false`

2. **For Status Field**:
   - Select service_status column
   - Data → Data Validation
   - Allow: List
   - Source: `active,archived,draft`

### Step 4: Format Numbers
1. **For Price Fields** (base_price, tier_price):
   - Select price columns
   - Right-click → Format Cells → Number
   - Category: Currency or Number with 2 decimal places

2. **For Duration Fields** (base_duration, tier_duration):
   - Select duration columns
   - Right-click → Format Cells → Number
   - Category: Number with 0 decimal places

## 📊 Google Sheets Instructions

### Step 1: Import CSV
1. Open Google Sheets
2. File → Import
3. Upload your CSV file
4. Choose "Replace spreadsheet" or "Insert new sheet"
5. Separator type: Comma

### Step 2: Apply Conditional Formatting
1. Select all data (Ctrl+A)
2. Format → Conditional formatting
3. Create rules for each column type:

**Required Fields (Red)**:
- Format cells if: Custom formula is
- Value: `=COLUMN()=MATCH("service_name",$1:$1,0)` (repeat for each required field)
- Formatting style: Red background

**ID Fields (Yellow)**:
- Format cells if: Custom formula is  
- Value: `=COLUMN()=MATCH("service_id",$1:$1,0)` (repeat for each ID field)
- Formatting style: Yellow background

### Step 3: Data Validation in Google Sheets
1. **Boolean Fields**:
   - Select column range
   - Data → Data validation
   - Criteria: List of items
   - Items: `true,false`

2. **Status Field**:
   - Select service_status column
   - Data → Data validation
   - Criteria: List of items
   - Items: `active,archived,draft`

## 🎯 Visual Layout Tips

### Header Row Formatting
- Make header row bold
- Apply a darker background color
- Freeze the header row (View → Freeze → 1 row)

### Column Width Optimization
- Auto-resize columns: Select all → Double-click column border
- Set minimum widths for readability:
  - ID columns: 250px
  - Name/Description: 200px
  - Numbers: 80px
  - Boolean: 60px

### Row Grouping
- Group rows by service for easier editing
- Use Excel's Group feature (Data → Group)
- Or add borders between different services

## 🔍 Data Entry Best Practices

### New Service Entry
1. **Start with a blank row**
2. **Leave service_id and tier_id blank** (🟡 yellow columns)
3. **Fill all red columns** (🔴 required fields)
4. **Add multiple rows for multiple tiers**

### Existing Service Updates
1. **Never change yellow columns** (🟡 ID fields)
2. **Only modify green columns** (🟢 editable fields)
3. **Verify tier_is_default** - only one per service

### Visual Verification
- **Red columns**: No empty cells (except for existing records where not applicable)
- **Yellow columns**: No modifications to existing UUIDs
- **Green columns**: Safe to edit as needed
- **Blue columns**: Optional, can be empty

## 🚨 Common Formatting Mistakes

### ❌ Avoid These:
1. **Changing cell formats** that affect data interpretation
2. **Adding extra columns** not in the original structure
3. **Merging cells** or changing row structure
4. **Using formulas** in data cells
5. **Changing date formats** in timestamp columns

### ✅ Do This Instead:
1. **Keep original CSV structure** intact
2. **Only modify cell values**, not formats
3. **Use data validation** for consistent entries
4. **Save as CSV** when done editing
5. **Test with small changes** first

## 💾 Saving and Export

### Excel:
1. File → Save As
2. Choose "CSV (Comma delimited) (*.csv)"
3. Keep original filename or use descriptive name
4. Click "Yes" when prompted about CSV format

### Google Sheets:
1. File → Download
2. Choose "Comma-separated values (.csv, current sheet)"
3. File will download to your computer

## 🔄 Import Preparation Checklist

Before importing your edited CSV:

- [ ] All red columns (required fields) are filled
- [ ] No yellow columns (ID fields) were modified for existing records
- [ ] Boolean fields use true/false format
- [ ] Status fields use valid values (active/archived/draft)
- [ ] Price fields use decimal format (25.00)
- [ ] Only one tier_is_default=true per service
- [ ] No duplicate tier names within the same service
- [ ] File saved as CSV format

## 📞 Support

If you encounter formatting issues:
1. Check that your CSV structure matches the original export
2. Verify all required fields are properly filled
3. Ensure data types match the expected formats
4. Test import with a small subset of changes first

Remember: **When in doubt, make small test changes first!**
