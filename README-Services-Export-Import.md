# Services Export/Import System - Ocean Soul Sparkles

## 🎯 Overview

The restructured services export/import system now supports easy bulk editing of services and their pricing tier variations through an **expanded CSV format**. This allows non-technical users to manage complex service structures through familiar spreadsheet tools.

## ✨ Key Improvements

### 1. **Expanded CSV Format (Default)**
- Each pricing tier appears as a separate row for clear visibility
- Easy to add, edit, or remove pricing tiers
- Clear relationship between services and their variations

### 2. **Enhanced Data Structure**
- All necessary fields included for comprehensive editing
- Support for new services with multiple pricing tiers
- Proper handling of service categories and visibility settings

### 3. **Robust Import Logic**
- Handles service-tier combinations intelligently
- Supports creating new services and updating existing ones
- Validates data integrity before import

### 4. **Fixed CSV Formatting**
- Proper escaping of special characters and newlines
- Clean, importable CSV files
- No more parsing errors from malformed data

### 5. **Comprehensive User Guide System**
- **Embedded CSV Guide**: Instructions included directly in export files
- **Color Coding System**: Visual indicators for field types and safety
- **Excel/Google Sheets Support**: Detailed formatting instructions
- **Step-by-Step Examples**: Common use cases with sample data
- **Data Validation Rules**: Clear requirements and acceptable values

## 📊 Export Features

### Default Format: Expanded CSV
- **One row per service-tier combination**
- Shows all 26 hair braiding variations clearly
- Easy to understand and edit in spreadsheet applications

### Sample Structure:
```csv
service_id,service_name,tier_name,tier_duration,tier_price,tier_is_default
4bb610c5-...,Face Painting,Children,5,15,false
4bb610c5-...,Face Painting,Standard,10,40,true
4bb610c5-...,Face Painting,Professional,60,60,false
```

## 🔧 Import Capabilities

### Service Management
- ✅ Create new services with multiple pricing tiers
- ✅ Update existing service details
- ✅ Add new pricing tiers to existing services
- ✅ Modify pricing, duration, and descriptions
- ✅ Update visibility and category settings

### Data Validation
- ✅ Required field validation
- ✅ Data type checking (numbers, booleans)
- ✅ Relationship integrity (service-tier associations)
- ✅ Duplicate prevention

### Import Modes
1. **Create**: Only add new services/tiers
2. **Update**: Only modify existing items
3. **Upsert**: Create new and update existing (recommended)

## 📁 File Structure

```
/documentation/admin/
├── services-export-import-guide.md    # Comprehensive user guide
├── services-csv-editing-guide.md      # Detailed CSV editing instructions
├── excel-formatting-guide.md          # Excel/Google Sheets formatting guide
└── README-Services-Export-Import.md   # This overview

/pages/api/admin/inventory/services/
├── export.js                          # Enhanced export with embedded guide
└── import.js                          # Rewritten import logic with validation

/sample-files/
├── services_export_expanded_sample.csv     # Example of new format
├── services_export_sample_with_guide.csv   # CSV with embedded guide
└── csv-guide-template.csv                  # Template for guide integration
```

## 🚀 Quick Start

### For Users (Non-Technical)

1. **Export Current Data**
   ```
   Admin Panel → Services → Export → Download CSV
   ```

2. **Edit in Spreadsheet**
   - Open in Excel, Google Sheets, or similar
   - Add new rows for new services/tiers
   - Modify existing data as needed
   - Follow the field guide in documentation

3. **Import Updated Data**
   ```
   Admin Panel → Services → Import → Upload CSV
   ```

### For Developers

1. **Export API**
   ```javascript
   GET /api/admin/inventory/services/export?format=csv&style=expanded
   ```

2. **Import API**
   ```javascript
   POST /api/admin/inventory/services/import
   Content-Type: multipart/form-data
   Body: { file: csvFile, updateMode: 'upsert' }
   ```

## 📋 Field Reference

### Essential Fields
- `service_name` (required)
- `base_duration`, `base_price` (required for new services)
- `tier_name`, `tier_duration`, `tier_price` (for pricing tiers)

### Complete Field List
See `documentation/admin/services-export-import-guide.md` for full field descriptions and examples.

## 🎯 Use Cases

### 1. Bulk Service Creation
Add multiple new services with their pricing tiers in one import:
```csv
,Henna Art,Traditional henna application,45,35,,Basic,30,25,false,1
,Henna Art,Traditional henna application,45,35,,Standard,45,35,true,2
,Henna Art,Traditional henna application,45,35,,Premium,60,50,false,3
```

### 2. Pricing Updates
Update all pricing tiers for existing services:
```csv
service_id,tier_id,tier_price
4bb610c5-...,23db6217-...,18
4bb610c5-...,3233cf5d-...,25
4bb610c5-...,1b01d3d4-...,45
```

### 3. Service Restructuring
Reorganize services and their categories:
```csv
service_id,service_name,category_name,visible_public,visible_events
4bb610c5-...,Face Painting,Kids Services,true,true
1b1c3d5a-...,Body Painting,Adult Services,false,true
```

## 🔍 Troubleshooting

### Common Issues
- **Import errors**: Check field formatting and required fields
- **Missing tiers**: Ensure tier_name is not empty
- **Duplicate services**: Use existing service_id for updates

### Validation Messages
- Clear error messages indicate specific issues
- Row-by-row error reporting for easy fixing
- Import summary shows what was created/updated

## 📞 Support

For detailed instructions and examples, see:
- `documentation/admin/services-export-import-guide.md`
- Sample file: `services_export_expanded_sample.csv`

---

**System Status**: ✅ Ready for Production Use
**Last Updated**: 2025-06-21
**Version**: 2.0 (Expanded Format)
