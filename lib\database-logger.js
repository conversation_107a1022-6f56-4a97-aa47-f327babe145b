import { supabaseAdmin, getAdminClient } from '@/lib/supabase';

/**
 * Comprehensive Database Activity Logger
 * Tracks all database operations for audit and troubleshooting
 */

export class DatabaseLogger {
  constructor() {
    this.adminClient = getAdminClient() || supabaseAdmin;
  }

  /**
   * Log database operation
   */
  async logOperation(operation) {
    try {
      const logEntry = {
        id: operation.id || this.generateId(),
        operation_type: operation.type, // 'export', 'import', 'backup', 'restore'
        user_email: operation.user_email,
        request_id: operation.request_id,
        status: operation.status, // 'started', 'completed', 'failed'
        tables_affected: operation.tables_affected || [],
        records_affected: operation.records_affected || 0,
        operation_details: operation.details || {},
        error_message: operation.error_message,
        duration_seconds: operation.duration_seconds,
        ip_address: operation.ip_address,
        user_agent: operation.user_agent,
        backup_id: operation.backup_id,
        file_size_mb: operation.file_size_mb,
        created_at: new Date().toISOString()
      };

      const { error } = await this.adminClient
        .from('database_activity_log')
        .insert([logEntry]);

      if (error) {
        console.error('Failed to log database operation:', error);
        return { success: false, error: error.message };
      }

      return { success: true, log_id: logEntry.id };
    } catch (error) {
      console.error('Exception logging database operation:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update existing log entry
   */
  async updateOperation(logId, updates) {
    try {
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      const { error } = await this.adminClient
        .from('database_activity_log')
        .update(updateData)
        .eq('id', logId);

      if (error) {
        console.error('Failed to update log entry:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Exception updating log entry:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get operation history
   */
  async getOperationHistory(filters = {}) {
    try {
      let query = this.adminClient
        .from('database_activity_log')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.operation_type) {
        query = query.eq('operation_type', filters.operation_type);
      }
      if (filters.user_email) {
        query = query.eq('user_email', filters.user_email);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.date_from) {
        query = query.gte('created_at', filters.date_from);
      }
      if (filters.date_to) {
        query = query.lte('created_at', filters.date_to);
      }

      // Limit results
      const limit = filters.limit || 100;
      query = query.limit(limit);

      const { data, error } = await query;

      if (error) {
        console.error('Failed to get operation history:', error);
        return { success: false, error: error.message };
      }

      return {
        success: true,
        operations: data || [],
        total_count: (data || []).length
      };
    } catch (error) {
      console.error('Exception getting operation history:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Log import operation details
   */
  async logImportOperation(details) {
    const operation = {
      type: 'import',
      user_email: details.user_email,
      request_id: details.request_id,
      status: details.status,
      tables_affected: details.tables_affected || [],
      records_affected: details.total_records_imported || 0,
      details: {
        update_mode: details.update_mode,
        file_name: details.file_name,
        file_size_mb: details.file_size_mb,
        backup_created: details.backup_created,
        backup_id: details.backup_id,
        import_results: details.import_results,
        validation_errors: details.validation_errors || []
      },
      error_message: details.error_message,
      duration_seconds: details.duration_seconds,
      backup_id: details.backup_id
    };

    return await this.logOperation(operation);
  }

  /**
   * Log export operation details
   */
  async logExportOperation(details) {
    const operation = {
      type: 'export',
      user_email: details.user_email,
      request_id: details.request_id,
      status: details.status,
      tables_affected: details.tables_exported || [],
      records_affected: details.total_records_exported || 0,
      details: {
        export_format: details.export_format,
        include_guide: details.include_guide,
        file_size_mb: details.file_size_mb,
        tables_requested: details.tables_requested
      },
      error_message: details.error_message,
      duration_seconds: details.duration_seconds,
      file_size_mb: details.file_size_mb
    };

    return await this.logOperation(operation);
  }

  /**
   * Log backup operation details
   */
  async logBackupOperation(details) {
    const operation = {
      type: 'backup',
      user_email: details.user_email,
      request_id: details.request_id,
      status: details.status,
      tables_affected: details.tables_included || [],
      records_affected: details.total_records || 0,
      details: {
        backup_type: details.backup_type,
        storage_path: details.storage_path,
        backup_size_mb: details.backup_size_mb,
        metadata: details.metadata || {}
      },
      error_message: details.error_message,
      duration_seconds: details.duration_seconds,
      backup_id: details.backup_id,
      file_size_mb: details.backup_size_mb
    };

    return await this.logOperation(operation);
  }

  /**
   * Log restore operation details
   */
  async logRestoreOperation(details) {
    const operation = {
      type: 'restore',
      user_email: details.user_email,
      request_id: details.request_id,
      status: details.status,
      tables_affected: details.tables_restored || [],
      records_affected: details.total_records_restored || 0,
      details: {
        source_backup_id: details.source_backup_id,
        pre_restore_backup_id: details.pre_restore_backup_id,
        restore_options: details.restore_options || {},
        restore_results: details.restore_results || {}
      },
      error_message: details.error_message,
      duration_seconds: details.duration_seconds,
      backup_id: details.source_backup_id
    };

    return await this.logOperation(operation);
  }

  /**
   * Generate unique operation ID
   */
  generateId() {
    return `op_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  /**
   * Get operation statistics
   */
  async getOperationStats(timeframe = '30 days') {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - parseInt(timeframe));

      const { data, error } = await this.adminClient
        .from('database_activity_log')
        .select('operation_type, status, records_affected, created_at')
        .gte('created_at', cutoffDate.toISOString());

      if (error) {
        console.error('Failed to get operation stats:', error);
        return { success: false, error: error.message };
      }

      const stats = {
        total_operations: data.length,
        successful_operations: data.filter(op => op.status === 'completed').length,
        failed_operations: data.filter(op => op.status === 'failed').length,
        total_records_affected: data.reduce((sum, op) => sum + (op.records_affected || 0), 0),
        operations_by_type: {},
        operations_by_day: {}
      };

      // Group by operation type
      data.forEach(op => {
        if (!stats.operations_by_type[op.operation_type]) {
          stats.operations_by_type[op.operation_type] = {
            total: 0,
            successful: 0,
            failed: 0
          };
        }
        stats.operations_by_type[op.operation_type].total++;
        if (op.status === 'completed') {
          stats.operations_by_type[op.operation_type].successful++;
        } else if (op.status === 'failed') {
          stats.operations_by_type[op.operation_type].failed++;
        }
      });

      // Group by day
      data.forEach(op => {
        const day = op.created_at.split('T')[0];
        if (!stats.operations_by_day[day]) {
          stats.operations_by_day[day] = 0;
        }
        stats.operations_by_day[day]++;
      });

      return { success: true, stats };
    } catch (error) {
      console.error('Exception getting operation stats:', error);
      return { success: false, error: error.message };
    }
  }
}

// Export singleton instance
export const databaseLogger = new DatabaseLogger();
