# 🛡️ SERVICES CSV EDITING GUIDE - READ BEFORE EDITING

## ⚠️ CRITICAL WARNING - READ FIRST

**🚨 PERMANENT DATA CHANGES**: Any changes you make and import will **PERMANENTLY OVERWRITE** existing data in the database. While changes can be reverted, this requires technical intervention. **ALWAYS BACKUP YOUR DATA** before making changes.

**✅ RECOMMENDED WORKFLOW**:
1. Download current export as backup
2. Make small test changes first
3. Import test changes and verify results
4. Only then proceed with larger modifications

---

## 🎨 COLOR CODING SYSTEM

When editing in Excel/Google Sheets, use this color system:

| Color | Meaning | Action |
|-------|---------|--------|
| 🔴 **RED** | Required Fields | Must not be empty |
| 🟡 **YELLOW** | ID Fields | Do NOT modify for existing records |
| 🟢 **GREEN** | Editable Fields | Safe to modify |
| 🔵 **BLUE** | New Record Fields | Can be blank for new services/tiers |

---

## 📋 FIELD REFERENCE GUIDE

### 🔴 REQUIRED FIELDS (Must Not Be Empty)

| Field | Description | Example | Notes |
|-------|-------------|---------|-------|
| `service_name` | Service display name | "Face Painting" | Required for all rows |
| `base_duration` | Base service duration (minutes) | 10 | Required for new services |
| `base_price` | Base service price | 40.00 | Required for new services |

### 🟡 ID FIELDS (Do NOT Modify for Existing Records)

| Field | Description | When to Modify |
|-------|-------------|----------------|
| `service_id` | Unique service identifier | Leave blank for NEW services only |
| `tier_id` | Unique pricing tier identifier | Leave blank for NEW tiers only |
| `category_id` | Category identifier | Only if changing service category |

### 🟢 EDITABLE FIELDS (Safe to Modify)

| Field | Description | Acceptable Values | Example |
|-------|-------------|-------------------|---------|
| `service_description` | Service description | Any text | "Creative face painting for all ages" |
| `tier_name` | Pricing tier name | Any text | "Children", "Standard", "Premium" |
| `tier_description` | Tier description | Any text | "Face painting for children under 12" |
| `tier_duration` | Tier duration (minutes) | Numbers only | 5, 10, 30, 60 |
| `tier_price` | Tier price | Decimal numbers | 15.00, 25.50, 100.00 |
| `service_color` | Calendar display color | Hex color codes | #4ECDC4, #FF6B6B |
| `service_status` | Service availability | active, archived, draft | active |
| `service_featured` | Featured service flag | true, false, 1, 0 | true |

### 🔵 VISIBILITY FIELDS (Boolean Values)

| Field | Description | Values | Default |
|-------|-------------|--------|---------|
| `visible_public` | Show on public booking | true/false | true |
| `visible_pos` | Show in POS system | true/false | true |
| `visible_events` | Show for events | true/false | true |

### 📂 CATEGORY FIELDS

| Field | Description | Notes |
|-------|-------------|-------|
| `category_name` | Category display name | System will auto-resolve category_id |
| `category_description` | Category description | Read-only (from database) |
| `parent_category_name` | Parent category | Read-only (from database) |

---

## 🎯 COMMON USE CASES & EXAMPLES

### 1. 💰 UPDATE PRICING FOR EXISTING TIERS

**Scenario**: Increase all Face Painting prices by $5

```csv
service_id,tier_id,tier_price
4bb610c5-9d80-4b4a-b86f-a942545b1f12,23db6217-e62f-4254-81c2-5607f1a3f819,20.00
4bb610c5-9d80-4b4a-b86f-a942545b1f12,1b01d3d4-b1b0-4c73-9635-cefd34addf7b,45.00
```

**✅ Safe**: Only modifying tier_price for existing tiers
**⚠️ Warning**: Don't change service_id or tier_id values

### 2. ➕ ADD NEW SERVICE WITH MULTIPLE TIERS

**Scenario**: Add "Nail Art" service with 3 pricing tiers

```csv
service_id,service_name,service_description,base_duration,base_price,tier_id,tier_name,tier_duration,tier_price,tier_is_default,tier_sort_order
,Nail Art,Professional nail art services,30,25.00,,Basic,15,15.00,false,1
,Nail Art,Professional nail art services,30,25.00,,Standard,30,25.00,true,2
,Nail Art,Professional nail art services,30,25.00,,Premium,45,40.00,false,3
```

**✅ Safe**: service_id and tier_id are blank (new records)
**⚠️ Warning**: Ensure base_duration and base_price are provided

### 3. 🏷️ ADD NEW TIERS TO EXISTING SERVICE

**Scenario**: Add "Group Package" tier to existing Face Painting service

```csv
service_id,service_name,tier_id,tier_name,tier_duration,tier_price,tier_is_default,tier_sort_order
4bb610c5-9d80-4b4a-b86f-a942545b1f12,Face Painting,,Group Package,60,150.00,false,8
```

**✅ Safe**: Using existing service_id, blank tier_id for new tier
**⚠️ Warning**: Don't duplicate tier names within same service

---

## ✅ DATA VALIDATION RULES

### Required Field Validation
- `service_name`: Must not be empty
- `base_duration`: Required for new services (numbers only)
- `base_price`: Required for new services (decimal format)
- `tier_duration`: Required for pricing tiers (numbers only)
- `tier_price`: Required for pricing tiers (decimal format)

### Data Type Requirements
- **Numbers**: Use decimal format (25.00, not 25)
- **Booleans**: Use true/false or 1/0
- **Colors**: Use hex format (#FF6B6B)
- **Status**: Only active, archived, or draft

### Business Rules
- Each service can have only ONE default tier (tier_is_default=true)
- Tier names must be unique within each service
- Service names should be unique across all services
- Sort order should be sequential (1, 2, 3, etc.)

---

## 🚫 COMMON MISTAKES TO AVOID

### ❌ DON'T DO THIS:
1. **Modify existing IDs**: Never change service_id or tier_id for existing records
2. **Mix service data**: Don't put different services in the same row
3. **Duplicate tier names**: Each tier within a service must have unique name
4. **Invalid status values**: Only use active, archived, or draft
5. **Missing required data**: New services need base_duration and base_price

### ✅ DO THIS INSTEAD:
1. **Leave IDs unchanged**: Only modify editable fields
2. **One service per row group**: Keep related tiers together
3. **Unique tier names**: Use descriptive, unique names per service
4. **Valid status values**: Stick to the three allowed values
5. **Complete new records**: Provide all required fields for new services

---

## 🔄 IMPORT PROCESS STEPS

### Before Import:
1. ✅ Save your edited CSV file
2. ✅ Verify all required fields are filled
3. ✅ Check for duplicate tier names within services
4. ✅ Confirm boolean values are true/false format

### During Import:
1. Choose import mode:
   - **Create**: Only add new services/tiers
   - **Update**: Only modify existing records
   - **Upsert**: Add new AND update existing (recommended)

### After Import:
1. ✅ Review import summary for errors
2. ✅ Check services in admin panel
3. ✅ Test booking system with updated services
4. ✅ Verify pricing and availability

---

## 🆘 TROUBLESHOOTING

### Import Errors:
- **"Missing required field"**: Check service_name is provided
- **"Invalid number format"**: Use decimal format (25.00)
- **"Duplicate tier name"**: Ensure unique names within each service
- **"Service not found"**: Verify service_id exists for updates

### Data Issues:
- **Tiers not appearing**: Check tier_name is not empty
- **Wrong default tier**: Only one tier per service should be default
- **Category problems**: Use existing category names or leave blank

---

## 📞 SUPPORT

**Need Help?**
1. Check import results summary for specific errors
2. Validate CSV structure against examples above
3. Contact system administrator for complex changes
4. Keep backup of original export for recovery

**Remember**: When in doubt, make small test changes first!

---

**⚠️ FINAL REMINDER**: This system directly modifies your live service database. Always test with small changes before making bulk modifications.
