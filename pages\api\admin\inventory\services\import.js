import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import formidable from 'formidable';
import fs from 'fs';
import csvParser from 'csv-parser';
import { v4 as uuidv4 } from 'uuid';

// Disable default body parser for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

/**
 * API endpoint for importing services data
 * Supports CSV and JSON formats
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response with import results
 */
export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request
  const { authorized, error, user, role } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  try {
    // Parse form data
    const form = new formidable.IncomingForm({
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
    });

    const { fields, files } = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        resolve({ fields, files });
      });
    });

    // Check if file exists
    if (!files.file) {
      return res.status(400).json({ error: 'No file provided' });
    }

    const file = files.file;
    const updateMode = fields.updateMode?.[0] || 'create'; // 'create', 'update', 'upsert'
    
    // Validate file type
    const allowedTypes = ['text/csv', 'application/json'];
    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({ 
        error: 'Invalid file type. Only CSV and JSON files are allowed.' 
      });
    }

    let importData = [];

    // Parse file based on type
    if (file.mimetype === 'text/csv') {
      importData = await parseCSVFile(file.filepath);
    } else if (file.mimetype === 'application/json') {
      importData = await parseJSONFile(file.filepath);
    }

    // Validate import data
    const validationResult = validateServiceData(importData);
    if (!validationResult.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.errors
      });
    }

    // Process import based on update mode
    const importResult = await processServiceImport(importData, updateMode, user.id);

    // Clean up uploaded file
    fs.unlinkSync(file.filepath);

    return res.status(200).json({
      success: true,
      message: 'Services imported successfully',
      results: importResult
    });

  } catch (error) {
    console.error('Error importing services:', error);
    return res.status(500).json({
      error: 'Failed to import services',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}

/**
 * Parse CSV file and return array of objects
 */
async function parseCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csvParser())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });
}

/**
 * Parse JSON file and return array of objects
 */
async function parseJSONFile(filePath) {
  const fileContent = fs.readFileSync(filePath, 'utf8');
  const data = JSON.parse(fileContent);
  return Array.isArray(data) ? data : [data];
}

/**
 * Validate service data for expanded format
 */
function validateServiceData(data) {
  const errors = [];
  const requiredFields = ['service_name'];

  data.forEach((item, index) => {
    const rowErrors = [];

    // Check required fields
    requiredFields.forEach(field => {
      if (!item[field] || item[field].toString().trim() === '') {
        rowErrors.push(`Missing required field: ${field}`);
      }
    });

    // For new services (no service_id), require base service data
    if (!item.service_id || item.service_id.trim() === '') {
      if (!item.base_duration || isNaN(parseInt(item.base_duration))) {
        rowErrors.push('New services require valid base_duration (minutes)');
      }
      if (!item.base_price || isNaN(parseFloat(item.base_price))) {
        rowErrors.push('New services require valid base_price');
      }
    }

    // Validate tier data if provided
    if (item.tier_name && item.tier_name.trim() !== '' && item.tier_name !== 'Base') {
      if (!item.tier_duration || isNaN(parseInt(item.tier_duration))) {
        rowErrors.push('Pricing tiers require valid tier_duration (minutes)');
      }
      if (!item.tier_price || isNaN(parseFloat(item.tier_price))) {
        rowErrors.push('Pricing tiers require valid tier_price');
      }
    }

    // Validate status
    if (item.service_status && !['active', 'archived', 'draft'].includes(item.service_status)) {
      rowErrors.push('Service status must be "active", "archived", or "draft"');
    }

    // Validate boolean fields
    ['service_featured', 'visible_public', 'visible_pos', 'visible_events', 'tier_is_default'].forEach(field => {
      if (item[field] && !['true', 'false', true, false, '1', '0'].includes(item[field])) {
        rowErrors.push(`${field} must be true/false or 1/0`);
      }
    });

    if (rowErrors.length > 0) {
      errors.push({
        row: index + 1,
        errors: rowErrors
      });
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Process service import for expanded format (service-tier combinations)
 */
async function processServiceImport(data, updateMode, userId) {
  const results = {
    servicesCreated: 0,
    servicesUpdated: 0,
    tiersCreated: 0,
    tiersUpdated: 0,
    skipped: 0,
    errors: []
  };

  // Group data by service
  const serviceGroups = {};
  data.forEach((item, index) => {
    const serviceKey = item.service_id?.trim() || item.service_name?.trim();
    if (!serviceGroups[serviceKey]) {
      serviceGroups[serviceKey] = [];
    }
    serviceGroups[serviceKey].push({ ...item, originalIndex: index });
  });

  // Process each service group
  for (const [serviceKey, serviceRows] of Object.entries(serviceGroups)) {
    try {
      // Get the first row as the main service data
      const mainRow = serviceRows[0];
      const serviceId = mainRow.service_id?.trim();

      // Helper function to parse boolean values
      const parseBoolean = (value) => {
        if (typeof value === 'boolean') return value;
        if (typeof value === 'string') {
          const lower = value.toLowerCase();
          return lower === 'true' || lower === '1' || lower === 'yes';
        }
        return false;
      };

      // Prepare service data with category validation
      let categoryId = mainRow.category_id?.trim() || null;
      let categoryName = mainRow.category_name?.trim() || '';

      // If category_id is missing but category_name exists, try to find the category_id
      if (!categoryId && categoryName) {
        const { data: categoryLookup } = await supabaseAdmin
          .from('service_categories')
          .select('id')
          .eq('name', categoryName)
          .single();

        if (categoryLookup) {
          categoryId = categoryLookup.id;
        }
      }

      const serviceData = {
        name: mainRow.service_name?.trim(),
        description: mainRow.service_description?.trim() || '',
        duration: parseInt(mainRow.base_duration) || 0,
        price: parseFloat(mainRow.base_price) || 0,
        color: mainRow.service_color?.trim() || '#6a0dad',
        category: categoryName,
        category_id: categoryId,
        image_url: mainRow.image_url?.trim() || '',
        status: mainRow.service_status?.trim() || 'active',
        featured: parseBoolean(mainRow.service_featured),
        visible_on_public: parseBoolean(mainRow.visible_public),
        visible_on_pos: parseBoolean(mainRow.visible_pos),
        visible_on_events: parseBoolean(mainRow.visible_events),
        meta_title: mainRow.meta_title?.trim() || '',
        meta_description: mainRow.meta_description?.trim() || '',
        booking_requirements: mainRow.booking_requirements?.trim() || '',
        availability_notes: mainRow.availability_notes?.trim() || '',
        updated_at: new Date().toISOString()
      };

      let currentServiceId = serviceId;
      let existingService = null;

      // Check if service exists
      if (serviceId) {
        const { data: serviceById } = await supabaseAdmin
          .from('services')
          .select('id, name')
          .eq('id', serviceId)
          .single();
        existingService = serviceById;
        currentServiceId = serviceId;
      } else {
        const { data: serviceByName } = await supabaseAdmin
          .from('services')
          .select('id, name')
          .eq('name', serviceData.name)
          .single();
        existingService = serviceByName;
        currentServiceId = existingService?.id;
      }

      // Handle service creation/update
      if (existingService) {
        if (updateMode === 'update' || updateMode === 'upsert') {
          const { error } = await supabaseAdmin
            .from('services')
            .update(serviceData)
            .eq('id', existingService.id);

          if (error) {
            results.errors.push({
              service: serviceData.name,
              error: `Failed to update service: ${error.message}`
            });
            continue;
          } else {
            results.servicesUpdated++;
          }
        } else if (updateMode === 'create') {
          results.skipped++;
          continue;
        }
      } else {
        if (updateMode === 'update') {
          results.skipped++;
          continue;
        } else if (updateMode === 'create' || updateMode === 'upsert') {
          currentServiceId = uuidv4();
          serviceData.id = currentServiceId;
          serviceData.created_at = new Date().toISOString();

          const { error } = await supabaseAdmin
            .from('services')
            .insert([serviceData]);

          if (error) {
            results.errors.push({
              service: serviceData.name,
              error: `Failed to create service: ${error.message}`
            });
            continue;
          } else {
            results.servicesCreated++;
          }
        }
      }

      // Process pricing tiers for this service
      await processPricingTiers(serviceRows, currentServiceId, updateMode, results);

    } catch (error) {
      results.errors.push({
        service: serviceKey,
        error: `Processing error: ${error.message}`
      });
    }
  }

  return results;
}

/**
 * Process pricing tiers for a service
 */
async function processPricingTiers(serviceRows, serviceId, updateMode, results) {
  for (const row of serviceRows) {
    // Skip rows that don't have tier data or are just base service rows
    if (!row.tier_name || row.tier_name.trim() === '' || row.tier_name === 'Base') {
      continue;
    }

    const tierData = {
      service_id: serviceId,
      name: row.tier_name?.trim(),
      description: row.tier_description?.trim() || '',
      duration: parseInt(row.tier_duration) || 0,
      price: parseFloat(row.tier_price) || 0,
      is_default: row.tier_is_default === 'true' || row.tier_is_default === true || row.tier_is_default === '1',
      sort_order: parseInt(row.tier_sort_order) || 0,
      updated_at: new Date().toISOString()
    };

    try {
      let existingTier = null;

      // Check if tier exists
      if (row.tier_id && row.tier_id.trim() !== '') {
        const { data: tierById } = await supabaseAdmin
          .from('service_pricing_tiers')
          .select('id, name')
          .eq('id', row.tier_id.trim())
          .single();
        existingTier = tierById;
      } else {
        // Look for tier by service_id and name
        const { data: tierByName } = await supabaseAdmin
          .from('service_pricing_tiers')
          .select('id, name')
          .eq('service_id', serviceId)
          .eq('name', tierData.name)
          .single();
        existingTier = tierByName;
      }

      if (existingTier) {
        // Update existing tier
        if (updateMode === 'update' || updateMode === 'upsert') {
          const { error } = await supabaseAdmin
            .from('service_pricing_tiers')
            .update(tierData)
            .eq('id', existingTier.id);

          if (error) {
            results.errors.push({
              tier: `${tierData.name} (${row.service_name})`,
              error: `Failed to update tier: ${error.message}`
            });
          } else {
            results.tiersUpdated++;
          }
        }
      } else {
        // Create new tier
        if (updateMode === 'create' || updateMode === 'upsert') {
          tierData.id = uuidv4();
          tierData.created_at = new Date().toISOString();

          const { error } = await supabaseAdmin
            .from('service_pricing_tiers')
            .insert([tierData]);

          if (error) {
            results.errors.push({
              tier: `${tierData.name} (${row.service_name})`,
              error: `Failed to create tier: ${error.message}`
            });
          } else {
            results.tiersCreated++;
          }
        }
      }
    } catch (error) {
      results.errors.push({
        tier: `${tierData.name} (${row.service_name})`,
        error: `Tier processing error: ${error.message}`
      });
    }
  }
}
