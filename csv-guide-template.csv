SERVICES CSV EDITING GUIDE - READ BEFORE MAKING CHANGES,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
⚠️ CRITICAL WARNING,⚠️ CRITIC<PERSON> WARNING,⚠️ CRITICAL WARNING,⚠️ CRITIC<PERSON> WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING,⚠️ CRITICAL WARNING
IMPORTING CHANGES WILL PERMANENTLY OVERWRITE DATABASE DATA,,,,,,,,,,,,,,,,,,,,,,,,
ALWAYS BACKUP YOUR DATA BEFORE MAKING CHANGES,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
🎨 COLOR CODING SYSTEM FOR EXCEL/GOOGLE SHEETS,,,,,,,,,,,,,,,,,,,,,,,,
🔴 RED = Required Fields (Must not be empty),,,,,,,,,,,,,,,,,,,,,,,,
🟡 YELLOW = ID Fields (Do NOT modify for existing records),,,,,,,,,,,,,,,,,,,,,,,,
🟢 GREEN = Editable Fields (Safe to modify),,,,,,,,,,,,,,,,,,,,,,,,
🔵 BLUE = New Record Fields (Can be blank for new items),,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
📋 FIELD REFERENCE GUIDE,,,,,,,,,,,,,,,,,,,,,,,,
Field Name,Color Code,Description,Acceptable Values,Example,Notes
service_id,🟡 YELLOW,Unique service identifier,UUID or blank,4bb610c5-9d80-4b4a-b86f-a942545b1f12,Leave blank for NEW services only
service_name,🔴 RED,Service display name,Any text,Face Painting,Required for all rows
service_description,🟢 GREEN,Service description,Any text,Creative face painting for all ages,Safe to modify
base_duration,🔴 RED,Base duration in minutes,Numbers only,10,Required for new services
base_price,🔴 RED,Base service price,Decimal numbers,40.00,Required for new services
tier_id,🟡 YELLOW,Unique tier identifier,UUID or blank,23db6217-e62f-4254-81c2-5607f1a3f819,Leave blank for NEW tiers only
tier_name,🟢 GREEN,Pricing tier name,Any text,Children / Standard / Premium,Must be unique within each service
tier_description,🟢 GREEN,Tier description,Any text,Face painting for children under 12,Safe to modify
tier_duration,🔴 RED,Tier duration in minutes,Numbers only,5,Required for pricing tiers
tier_price,🔴 RED,Tier price,Decimal numbers,15.00,Required for pricing tiers
tier_is_default,🟢 GREEN,Default tier flag,true/false or 1/0,true,Only ONE per service should be true
tier_sort_order,🟢 GREEN,Display order,Numbers,1,Sequential numbering (1 2 3 etc)
category_id,🔵 BLUE,Category identifier,UUID or blank,429b3ff6-ae06-4fa0-af2a-448a47da667d,System auto-resolves from category_name
category_name,🟢 GREEN,Category display name,Existing category names,Face Painting,System will find matching category_id
service_color,🟢 GREEN,Calendar display color,Hex color codes,#4ECDC4,Use # followed by 6 characters
service_status,🟢 GREEN,Service availability,active / archived / draft,active,Controls service visibility
service_featured,🟢 GREEN,Featured service flag,true/false or 1/0,true,Highlights service in listings
visible_public,🟢 GREEN,Show on public booking,true/false or 1/0,true,Controls public website visibility
visible_pos,🟢 GREEN,Show in POS system,true/false or 1/0,true,Controls point-of-sale visibility
visible_events,🟢 GREEN,Show for events,true/false or 1/0,true,Controls event booking visibility
,,,,,,
🎯 COMMON USE CASES & EXAMPLES,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
1. UPDATE PRICING FOR EXISTING TIERS,,,,,,,,,,,,,,,,,,,,,,,,
Example: Increase Face Painting prices by $5,,,,,,,,,,,,,,,,,,,,,,,,
service_id,tier_id,tier_price,,,,,,,,,,,,,,,,,,,,,
4bb610c5-9d80-4b4a-b86f-a942545b1f12,23db6217-e62f-4254-81c2-5607f1a3f819,20.00,,,,,,,,,,,,,,,,,,,,,
4bb610c5-9d80-4b4a-b86f-a942545b1f12,1b01d3d4-b1b0-4c73-9635-cefd34addf7b,45.00,,,,,,,,,,,,,,,,,,,,,
✅ Safe: Only modifying tier_price for existing tiers,,,,,,,,,,,,,,,,,,,,,,,,
⚠️ Warning: Don't change service_id or tier_id values,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
2. ADD NEW SERVICE WITH MULTIPLE TIERS,,,,,,,,,,,,,,,,,,,,,,,,
Example: Add Nail Art service with 3 pricing tiers,,,,,,,,,,,,,,,,,,,,,,,,
service_id,service_name,service_description,base_duration,base_price,tier_id,tier_name,tier_duration,tier_price,tier_is_default,tier_sort_order,,,,,,,,,,,,,
,Nail Art,Professional nail art services,30,25.00,,Basic,15,15.00,false,1,,,,,,,,,,,,,
,Nail Art,Professional nail art services,30,25.00,,Standard,30,25.00,true,2,,,,,,,,,,,,,
,Nail Art,Professional nail art services,30,25.00,,Premium,45,40.00,false,3,,,,,,,,,,,,,
✅ Safe: service_id and tier_id are blank (new records),,,,,,,,,,,,,,,,,,,,,,,,
⚠️ Warning: Ensure base_duration and base_price are provided,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
3. ADD NEW TIER TO EXISTING SERVICE,,,,,,,,,,,,,,,,,,,,,,,,
Example: Add Group Package tier to Face Painting,,,,,,,,,,,,,,,,,,,,,,,,
service_id,service_name,tier_id,tier_name,tier_duration,tier_price,tier_is_default,tier_sort_order,,,,,,,,,,,,,,,,
4bb610c5-9d80-4b4a-b86f-a942545b1f12,Face Painting,,Group Package,60,150.00,false,8,,,,,,,,,,,,,,,,
✅ Safe: Using existing service_id with blank tier_id,,,,,,,,,,,,,,,,,,,,,,,,
⚠️ Warning: Don't duplicate tier names within same service,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
✅ DATA VALIDATION RULES,,,,,,,,,,,,,,,,,,,,,,,,
Required Fields: service_name base_duration base_price (for new services),,,,,,,,,,,,,,,,,,,,,,,,
Required Fields: tier_duration tier_price (for pricing tiers),,,,,,,,,,,,,,,,,,,,,,,,
Number Format: Use decimals (25.00 not 25),,,,,,,,,,,,,,,,,,,,,,,,
Boolean Format: Use true/false or 1/0,,,,,,,,,,,,,,,,,,,,,,,,
Color Format: Use hex codes (#FF6B6B),,,,,,,,,,,,,,,,,,,,,,,,
Status Values: Only active archived or draft,,,,,,,,,,,,,,,,,,,,,,,,
Business Rules: Only ONE default tier per service,,,,,,,,,,,,,,,,,,,,,,,,
Business Rules: Unique tier names within each service,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
🚫 COMMON MISTAKES TO AVOID,,,,,,,,,,,,,,,,,,,,,,,,
❌ DON'T modify existing service_id or tier_id values,,,,,,,,,,,,,,,,,,,,,,,,
❌ DON'T mix different services in the same row,,,,,,,,,,,,,,,,,,,,,,,,
❌ DON'T use duplicate tier names within a service,,,,,,,,,,,,,,,,,,,,,,,,
❌ DON'T use invalid status values (only active/archived/draft),,,,,,,,,,,,,,,,,,,,,,,,
❌ DON'T leave required fields empty for new records,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
✅ IMPORT PROCESS CHECKLIST,,,,,,,,,,,,,,,,,,,,,,,,
Before Import:,,,,,,,,,,,,,,,,,,,,,,,,
✅ Save your edited CSV file,,,,,,,,,,,,,,,,,,,,,,,,
✅ Verify all required fields are filled,,,,,,,,,,,,,,,,,,,,,,,,
✅ Check for duplicate tier names within services,,,,,,,,,,,,,,,,,,,,,,,,
✅ Confirm boolean values are true/false format,,,,,,,,,,,,,,,,,,,,,,,,
During Import:,,,,,,,,,,,,,,,,,,,,,,,,
Choose import mode: Create (new only) Update (existing only) or Upsert (both),,,,,,,,,,,,,,,,,,,,,,,,
After Import:,,,,,,,,,,,,,,,,,,,,,,,,
✅ Review import summary for errors,,,,,,,,,,,,,,,,,,,,,,,,
✅ Check services in admin panel,,,,,,,,,,,,,,,,,,,,,,,,
✅ Test booking system with updated services,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
🆘 TROUBLESHOOTING COMMON ERRORS,,,,,,,,,,,,,,,,,,,,,,,,
Missing required field → Check service_name is provided,,,,,,,,,,,,,,,,,,,,,,,,
Invalid number format → Use decimal format (25.00),,,,,,,,,,,,,,,,,,,,,,,,
Duplicate tier name → Ensure unique names within each service,,,,,,,,,,,,,,,,,,,,,,,,
Service not found → Verify service_id exists for updates,,,,,,,,,,,,,,,,,,,,,,,,
Tiers not appearing → Check tier_name is not empty,,,,,,,,,,,,,,,,,,,,,,,,
Wrong default tier → Only one tier per service should be default,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
📞 NEED HELP?,,,,,,,,,,,,,,,,,,,,,,,,
1. Check import results summary for specific errors,,,,,,,,,,,,,,,,,,,,,,,,
2. Validate CSV structure against examples above,,,,,,,,,,,,,,,,,,,,,,,,
3. Contact system administrator for complex changes,,,,,,,,,,,,,,,,,,,,,,,,
4. Keep backup of original export for recovery,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
⚠️ FINAL REMINDER: When in doubt make small test changes first!,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,
=== END OF GUIDE - YOUR DATA STARTS BELOW ===,,,,,,,,,,,,,,,,,,,,,,,,
