import { supabaseAdmin, getAdminClient } from '@/lib/supabase';

/**
 * Automated Database Backup System
 * Creates complete database backups before import operations
 * Manages rolling retention policy and Supabase storage
 */

export class DatabaseBackupManager {
  constructor() {
    this.adminClient = getAdminClient() || supabaseAdmin;
    this.bucketName = 'database-backups';
    this.maxBackups = 5;
    this.retentionDays = 90; // 3 months
  }

  /**
   * Create a complete database backup before import operations
   */
  async createBackup(metadata = {}) {
    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    console.log(`[Backup] Creating backup: ${backupId}`);

    try {
      // Define core tables to backup
      const coreTables = [
        'services', 'service_pricing_tiers', 'service_categories',
        'customers', 'bookings', 'products', 'orders', 'order_items',
        'payments', 'locations', 'inventory'
      ];

      const backupData = {
        backup_id: backupId,
        created_at: new Date().toISOString(),
        created_by: metadata.user_email || 'system',
        backup_type: metadata.backup_type || 'pre_import',
        tables_included: coreTables,
        metadata: metadata,
        data: {}
      };

      // Export data from each table
      let totalRecords = 0;
      for (const tableName of coreTables) {
        console.log(`[Backup] Backing up table: ${tableName}`);
        
        try {
          const { data, error } = await this.adminClient
            .from(tableName)
            .select('*')
            .order('created_at', { ascending: false });

          if (error) {
            console.error(`[Backup] Error backing up ${tableName}:`, error);
            backupData.data[tableName] = {
              records: [],
              error: error.message,
              backed_up_at: new Date().toISOString()
            };
          } else {
            backupData.data[tableName] = {
              records: data || [],
              record_count: (data || []).length,
              backed_up_at: new Date().toISOString()
            };
            totalRecords += (data || []).length;
          }
        } catch (tableError) {
          console.error(`[Backup] Exception backing up ${tableName}:`, tableError);
          backupData.data[tableName] = {
            records: [],
            error: tableError.message,
            backed_up_at: new Date().toISOString()
          };
        }
      }

      backupData.total_records = totalRecords;
      backupData.backup_size_mb = JSON.stringify(backupData).length / (1024 * 1024);

      // Store backup in Supabase storage
      const backupFileName = `${backupId}.json`;
      const { data: uploadData, error: uploadError } = await this.adminClient.storage
        .from(this.bucketName)
        .upload(backupFileName, JSON.stringify(backupData, null, 2), {
          contentType: 'application/json',
          upsert: false
        });

      if (uploadError) {
        console.error(`[Backup] Failed to upload backup:`, uploadError);
        throw new Error(`Backup upload failed: ${uploadError.message}`);
      }

      // Record backup metadata in database
      const { error: metadataError } = await this.adminClient
        .from('backup_monitoring')
        .insert([{
          backup_id: backupId,
          backup_type: backupData.backup_type,
          created_by: backupData.created_by,
          total_records: totalRecords,
          backup_size_mb: backupData.backup_size_mb,
          storage_path: backupFileName,
          tables_included: coreTables,
          metadata: metadata,
          status: 'completed',
          created_at: backupData.created_at
        }]);

      if (metadataError) {
        console.error(`[Backup] Failed to record backup metadata:`, metadataError);
        // Don't fail the backup for metadata issues
      }

      // Clean up old backups
      await this.cleanupOldBackups();

      console.log(`[Backup] Backup completed: ${backupId} (${totalRecords} records, ${backupData.backup_size_mb.toFixed(2)} MB)`);

      return {
        success: true,
        backup_id: backupId,
        total_records: totalRecords,
        backup_size_mb: backupData.backup_size_mb,
        storage_path: backupFileName
      };

    } catch (error) {
      console.error(`[Backup] Critical backup error:`, error);
      
      // Record failed backup
      try {
        await this.adminClient
          .from('backup_monitoring')
          .insert([{
            backup_id: backupId,
            backup_type: metadata.backup_type || 'pre_import',
            created_by: metadata.user_email || 'system',
            status: 'failed',
            error_message: error.message,
            created_at: new Date().toISOString()
          }]);
      } catch (metaError) {
        console.error(`[Backup] Failed to record backup failure:`, metaError);
      }

      return {
        success: false,
        error: error.message,
        backup_id: backupId
      };
    }
  }

  /**
   * Clean up old backups based on retention policy
   */
  async cleanupOldBackups() {
    try {
      console.log(`[Backup] Starting cleanup - keeping ${this.maxBackups} recent backups`);

      // Get all backups ordered by creation date
      const { data: backups, error } = await this.adminClient
        .from('backup_monitoring')
        .select('backup_id, storage_path, created_at')
        .eq('status', 'completed')
        .order('created_at', { ascending: false });

      if (error) {
        console.error(`[Backup] Error fetching backups for cleanup:`, error);
        return;
      }

      if (!backups || backups.length <= this.maxBackups) {
        console.log(`[Backup] No cleanup needed (${backups?.length || 0} backups)`);
        return;
      }

      // Identify backups to delete (keep most recent maxBackups)
      const backupsToDelete = backups.slice(this.maxBackups);
      console.log(`[Backup] Deleting ${backupsToDelete.length} old backups`);

      for (const backup of backupsToDelete) {
        try {
          // Delete from storage
          const { error: deleteError } = await this.adminClient.storage
            .from(this.bucketName)
            .remove([backup.storage_path]);

          if (deleteError) {
            console.error(`[Backup] Failed to delete backup file ${backup.storage_path}:`, deleteError);
          }

          // Update database record
          const { error: updateError } = await this.adminClient
            .from('backup_monitoring')
            .update({ 
              status: 'deleted',
              deleted_at: new Date().toISOString()
            })
            .eq('backup_id', backup.backup_id);

          if (updateError) {
            console.error(`[Backup] Failed to update backup record ${backup.backup_id}:`, updateError);
          }

          console.log(`[Backup] Deleted backup: ${backup.backup_id}`);
        } catch (deleteError) {
          console.error(`[Backup] Exception deleting backup ${backup.backup_id}:`, deleteError);
        }
      }

      // Also clean up backups older than retention period
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);

      const { data: oldBackups } = await this.adminClient
        .from('backup_monitoring')
        .select('backup_id, storage_path')
        .eq('status', 'completed')
        .lt('created_at', cutoffDate.toISOString());

      if (oldBackups && oldBackups.length > 0) {
        console.log(`[Backup] Deleting ${oldBackups.length} backups older than ${this.retentionDays} days`);
        
        for (const oldBackup of oldBackups) {
          try {
            await this.adminClient.storage
              .from(this.bucketName)
              .remove([oldBackup.storage_path]);

            await this.adminClient
              .from('backup_monitoring')
              .update({ 
                status: 'deleted',
                deleted_at: new Date().toISOString()
              })
              .eq('backup_id', oldBackup.backup_id);

            console.log(`[Backup] Deleted old backup: ${oldBackup.backup_id}`);
          } catch (error) {
            console.error(`[Backup] Error deleting old backup ${oldBackup.backup_id}:`, error);
          }
        }
      }

    } catch (error) {
      console.error(`[Backup] Cleanup error:`, error);
    }
  }

  /**
   * List available backups
   */
  async listBackups() {
    try {
      const { data, error } = await this.adminClient
        .from('backup_monitoring')
        .select('*')
        .eq('status', 'completed')
        .order('created_at', { ascending: false });

      if (error) {
        console.error(`[Backup] Error listing backups:`, error);
        return { success: false, error: error.message };
      }

      return {
        success: true,
        backups: data || [],
        total_count: (data || []).length
      };
    } catch (error) {
      console.error(`[Backup] Exception listing backups:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Restore from a specific backup
   */
  async restoreFromBackup(backupId, options = {}) {
    console.log(`[Backup] Starting restore from backup: ${backupId}`);
    
    try {
      // Get backup metadata
      const { data: backupMeta, error: metaError } = await this.adminClient
        .from('backup_monitoring')
        .select('*')
        .eq('backup_id', backupId)
        .eq('status', 'completed')
        .single();

      if (metaError || !backupMeta) {
        throw new Error(`Backup not found: ${backupId}`);
      }

      // Download backup data
      const { data: backupFile, error: downloadError } = await this.adminClient.storage
        .from(this.bucketName)
        .download(backupMeta.storage_path);

      if (downloadError) {
        throw new Error(`Failed to download backup: ${downloadError.message}`);
      }

      const backupData = JSON.parse(await backupFile.text());

      // Restore each table
      const restoreResults = {};
      const tablesToRestore = options.tables || Object.keys(backupData.data);

      for (const tableName of tablesToRestore) {
        if (!backupData.data[tableName] || !backupData.data[tableName].records) {
          console.log(`[Backup] Skipping ${tableName} - no data in backup`);
          continue;
        }

        console.log(`[Backup] Restoring table: ${tableName}`);
        
        try {
          const records = backupData.data[tableName].records;
          
          if (options.truncateFirst) {
            // Clear existing data
            await this.adminClient
              .from(tableName)
              .delete()
              .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all
          }

          // Insert backup data in batches
          const batchSize = 100;
          let restored = 0;
          
          for (let i = 0; i < records.length; i += batchSize) {
            const batch = records.slice(i, i + batchSize);
            const { error: insertError } = await this.adminClient
              .from(tableName)
              .upsert(batch, { onConflict: 'id' });

            if (insertError) {
              console.error(`[Backup] Error restoring batch for ${tableName}:`, insertError);
            } else {
              restored += batch.length;
            }
          }

          restoreResults[tableName] = {
            success: true,
            records_restored: restored,
            total_records: records.length
          };

          console.log(`[Backup] Restored ${restored}/${records.length} records for ${tableName}`);
        } catch (tableError) {
          console.error(`[Backup] Error restoring ${tableName}:`, tableError);
          restoreResults[tableName] = {
            success: false,
            error: tableError.message
          };
        }
      }

      console.log(`[Backup] Restore completed from backup: ${backupId}`);
      
      return {
        success: true,
        backup_id: backupId,
        restore_results: restoreResults,
        restored_at: new Date().toISOString()
      };

    } catch (error) {
      console.error(`[Backup] Restore error:`, error);
      return {
        success: false,
        error: error.message,
        backup_id: backupId
      };
    }
  }
}

// Export singleton instance
export const backupManager = new DatabaseBackupManager();
