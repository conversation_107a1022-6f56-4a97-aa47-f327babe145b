#!/usr/bin/env node

/**
 * Simple test script for database export functionality
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';
const TEST_OUTPUT_DIR = path.join(__dirname, '../test-results');

// Ensure test output directory exists
if (!fs.existsSync(TEST_OUTPUT_DIR)) {
  fs.mkdirSync(TEST_OUTPUT_DIR, { recursive: true });
}

/**
 * Test export functionality
 */
async function testExport(format = 'json', tables = 'all') {
  console.log(`\n🧪 Testing ${format.toUpperCase()} export...`);
  
  return new Promise((resolve, reject) => {
    const url = `/api/admin/database/export?format=${format}&tables=${tables}&includeGuide=true`;
    console.log(`📡 Making request to: ${BASE_URL}${url}`);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: url,
      method: 'GET',
      headers: {
        'Accept': format === 'json' ? 'application/json' : 'text/csv',
        'User-Agent': 'DatabaseExportTest/1.0'
      }
    };

    const req = http.request(options, (res) => {
      console.log(`📊 Response status: ${res.statusCode} ${res.statusMessage}`);
      console.log(`📋 Response headers:`);
      
      // Check headers
      Object.keys(res.headers).forEach(key => {
        console.log(`   ${key}: ${res.headers[key]}`);
      });

      // Verify required headers for file download
      const contentDisposition = res.headers['content-disposition'];
      const contentType = res.headers['content-type'];
      
      if (!contentDisposition || !contentDisposition.includes('attachment')) {
        console.error(`❌ Missing or invalid Content-Disposition header: ${contentDisposition}`);
        resolve(false);
        return;
      }

      if (!contentType) {
        console.error(`❌ Missing Content-Type header`);
        resolve(false);
        return;
      }

      // Extract filename from Content-Disposition header
      const filenameMatch = contentDisposition.match(/filename="([^"]+)"/);
      const filename = filenameMatch ? filenameMatch[1] : `test_export.${format}`;
      
      console.log(`📁 Extracted filename: ${filename}`);

      // Read response content
      let content = '';
      res.on('data', (chunk) => {
        content += chunk;
      });

      res.on('end', () => {
        const contentLength = content.length;
        console.log(`📏 Content length: ${contentLength} characters`);

        // Log response content for debugging
        if (res.statusCode !== 200) {
          console.log(`📄 Response content: ${content.substring(0, 500)}...`);
        }

        if (contentLength === 0) {
          console.error(`❌ Empty response content`);
          resolve(false);
          return;
        }

        // Save test file
        const testFilePath = path.join(TEST_OUTPUT_DIR, `test_${filename}`);
        fs.writeFileSync(testFilePath, content, 'utf8');
        console.log(`💾 Test file saved to: ${testFilePath}`);

        // Validate content based on format
        if (format === 'json') {
          try {
            const jsonData = JSON.parse(content);
            if (!jsonData.metadata || !jsonData.tables) {
              console.error(`❌ Invalid JSON structure - missing metadata or tables`);
              resolve(false);
              return;
            }
            console.log(`✅ Valid JSON structure with ${Object.keys(jsonData.tables).length} tables`);
          } catch (error) {
            console.error(`❌ Invalid JSON content: ${error.message}`);
            resolve(false);
            return;
          }
        } else if (format === 'csv') {
          // Basic CSV validation
          const lines = content.split('\n');
          if (lines.length < 2) {
            console.error(`❌ CSV appears to be empty or invalid`);
            resolve(false);
            return;
          }
          console.log(`✅ CSV content with ${lines.length} lines`);
        } else if (format === 'excel') {
          // Basic Excel validation - check if it's a valid Excel file
          if (!content.startsWith('PK')) {
            console.error(`❌ Excel file appears to be invalid (missing Excel signature)`);
            resolve(false);
            return;
          }
          console.log(`✅ Excel file appears to be valid`);
        }

        console.log(`✅ ${format.toUpperCase()} export test passed!`);
        resolve(true);
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Export test failed: ${error.message}`);
      resolve(false);
    });

    req.end();
  });
}

/**
 * Main test runner
 */
async function runTests() {
  console.log(`🚀 Starting Database Export Tests`);
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📂 Test output directory: ${TEST_OUTPUT_DIR}`);

  const results = {
    jsonExport: false,
    csvExport: false,
    excelExport: false
  };

  try {
    // Test JSON export
    results.jsonExport = await testExport('json', 'all');
    
    // Test CSV export
    results.csvExport = await testExport('csv', 'services');
    
    // Test Excel export
    results.excelExport = await testExport('excel', 'all');

    // Summary
    console.log(`\n📊 Test Results Summary:`);
    console.log(`   JSON Export: ${results.jsonExport ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   CSV Export: ${results.csvExport ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Excel Export: ${results.excelExport ? '✅ PASS' : '❌ FAIL'}`);

    const allPassed = Object.values(results).every(result => result === true);
    
    if (allPassed) {
      console.log(`\n🎉 All tests passed! Export functionality is working correctly.`);
      process.exit(0);
    } else {
      console.log(`\n⚠️ Some tests failed. Please check the issues above.`);
      process.exit(1);
    }

  } catch (error) {
    console.error(`\n💥 Test runner failed: ${error.message}`);
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runTests();
}

module.exports = { testExport };
