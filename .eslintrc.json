{"root": true, "extends": "next/core-web-vitals", "rules": {"react/no-unescaped-entities": "off", "@next/next/no-img-element": "off", "react-hooks/exhaustive-deps": "off", "@next/next/no-html-link-for-pages": "off", "@next/next/no-sync-scripts": "off", "import/no-anonymous-default-export": "off"}, "ignorePatterns": ["node_modules/", ".next/", "out/", "build/", "dist/", "scripts/", "__tests__/", "*.config.js", "*.config.cjs"]}