import { supabaseAdmin, getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { backupManager } from '@/lib/database-backup';

/**
 * Backup Management API
 * List, create, and restore database backups
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Backup management request: ${req.method}`);

  // Authenticate request
  let authorized = false;
  let user = null;

  try {
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
      authorized = true;
      user = { id: 'dev-user', email: '<EMAIL>' };
    } else {
      const authResult = await authenticateAdminRequest(req);
      authorized = authResult.authorized;
      user = authResult.user;
    }
  } catch (error) {
    console.error(`[${requestId}] Authentication error:`, error);
    authorized = false;
  }

  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      requestId
    });
  }

  try {
    switch (req.method) {
      case 'GET':
        return await handleListBackups(req, res, requestId);
      
      case 'POST':
        return await handleCreateBackup(req, res, requestId, user);
      
      case 'PUT':
        return await handleRestoreBackup(req, res, requestId, user);
      
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error(`[${requestId}] Critical error in backup management:`, error);
    return res.status(500).json({
      error: 'Backup management failed',
      message: error.message,
      requestId
    });
  }
}

/**
 * List available backups
 */
async function handleListBackups(req, res, requestId) {
  console.log(`[${requestId}] Listing backups`);
  
  try {
    const result = await backupManager.listBackups();
    
    if (!result.success) {
      return res.status(500).json({
        error: 'Failed to list backups',
        details: result.error,
        requestId
      });
    }

    return res.status(200).json({
      success: true,
      backups: result.backups,
      total_count: result.total_count,
      requestId
    });
  } catch (error) {
    console.error(`[${requestId}] Error listing backups:`, error);
    return res.status(500).json({
      error: 'Failed to list backups',
      message: error.message,
      requestId
    });
  }
}

/**
 * Create a new backup
 */
async function handleCreateBackup(req, res, requestId, user) {
  console.log(`[${requestId}] Creating manual backup`);
  
  try {
    const { backup_type = 'manual', metadata = {} } = req.body;
    
    const backupMetadata = {
      ...metadata,
      user_email: user.email,
      backup_type,
      request_id: requestId,
      created_via: 'admin_interface'
    };

    const result = await backupManager.createBackup(backupMetadata);
    
    if (!result.success) {
      return res.status(500).json({
        error: 'Failed to create backup',
        details: result.error,
        requestId
      });
    }

    return res.status(200).json({
      success: true,
      backup_id: result.backup_id,
      total_records: result.total_records,
      backup_size_mb: result.backup_size_mb,
      storage_path: result.storage_path,
      requestId
    });
  } catch (error) {
    console.error(`[${requestId}] Error creating backup:`, error);
    return res.status(500).json({
      error: 'Failed to create backup',
      message: error.message,
      requestId
    });
  }
}

/**
 * Restore from a backup
 */
async function handleRestoreBackup(req, res, requestId, user) {
  const { backup_id, options = {} } = req.body;
  
  if (!backup_id) {
    return res.status(400).json({
      error: 'Backup ID is required',
      requestId
    });
  }

  console.log(`[${requestId}] Restoring from backup: ${backup_id}`);
  
  try {
    // Create a backup before restore (backup of current state)
    console.log(`[${requestId}] Creating pre-restore backup`);
    const preRestoreBackup = await backupManager.createBackup({
      user_email: user.email,
      backup_type: 'pre_restore',
      restore_from_backup_id: backup_id,
      request_id: requestId
    });

    if (!preRestoreBackup.success) {
      console.warn(`[${requestId}] Failed to create pre-restore backup:`, preRestoreBackup.error);
      // Continue with restore but warn user
    }

    // Perform the restore
    const restoreResult = await backupManager.restoreFromBackup(backup_id, options);
    
    if (!restoreResult.success) {
      return res.status(500).json({
        error: 'Failed to restore from backup',
        details: restoreResult.error,
        backup_id,
        pre_restore_backup_id: preRestoreBackup.backup_id,
        requestId
      });
    }

    return res.status(200).json({
      success: true,
      backup_id,
      restore_results: restoreResult.restore_results,
      restored_at: restoreResult.restored_at,
      pre_restore_backup_id: preRestoreBackup.backup_id,
      requestId
    });
  } catch (error) {
    console.error(`[${requestId}] Error restoring backup:`, error);
    return res.status(500).json({
      error: 'Failed to restore from backup',
      message: error.message,
      backup_id,
      requestId
    });
  }
}
