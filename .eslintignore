# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
.next/
out/
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Coverage reports
coverage/
.nyc_output/

# Temporary files
tmp/
temp/

# Cache directories
.cache/
.parcel-cache/

# PWA files
public/sw.js
public/workbox-*.js

# Generated files
public/sitemap.xml
public/robots.txt

# Test files that might have issues
__tests__/
*.test.js
*.test.jsx
*.spec.js
*.spec.jsx

# Scripts that might have Node.js specific code
scripts/
*.config.js
*.config.cjs

# Documentation
docs/
*.md

# Backup files
*.backup
*.bak

# Lock files
package-lock.json
yarn.lock

# Vercel
.vercel/

# TypeScript
*.tsbuildinfo
next-env.d.ts
