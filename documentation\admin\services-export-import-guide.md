# Services Export/Import Guide

## Overview

The Ocean Soul Sparkles services export/import system allows you to bulk manage your services and pricing tiers through spreadsheet editing. This guide explains how to use the expanded CSV format for easy editing and re-importing.

## Export Format

The system uses an **expanded CSV format** where each row represents either:
- A service with its base pricing (when no pricing tiers exist)
- A service-tier combination (when pricing tiers exist)

### Key Benefits
- **Easy Editing**: Each pricing tier appears as a separate row for clear visibility
- **Bulk Management**: Add multiple services and tiers in one import
- **Flexible Structure**: Mix new services with updates to existing ones

## CSV Structure

### Required Fields
- `service_name`: The name of the service (required for all rows)
- `base_duration`: Duration in minutes for new services
- `base_price`: Base price for new services

### Service Fields
| Field | Description | Example |
|-------|-------------|---------|
| `service_id` | Unique ID (leave blank for new services) | `c35117a3-a451-4884-8a29-c930372d8463` |
| `service_name` | Service name | `Face Painting` |
| `service_description` | Service description | `Creative face painting for all ages` |
| `base_duration` | Base duration in minutes | `10` |
| `base_price` | Base price | `40.00` |
| `service_color` | Color for calendar display | `#4ECDC4` |
| `service_status` | Status: active, archived, draft | `active` |
| `service_featured` | Featured service (true/false) | `true` |

### Pricing Tier Fields
| Field | Description | Example |
|-------|-------------|---------|
| `tier_id` | Unique tier ID (leave blank for new tiers) | `23db6217-e62f-4254-81c2-5607f1a3f819` |
| `tier_name` | Tier name | `Children` |
| `tier_description` | Tier description | `Face painting for children under 12` |
| `tier_duration` | Duration in minutes | `5` |
| `tier_price` | Tier price | `15.00` |
| `tier_is_default` | Default tier (true/false) | `false` |
| `tier_sort_order` | Display order | `1` |

### Category Fields
| Field | Description | Example |
|-------|-------------|---------|
| `category_id` | Category UUID | `429b3ff6-ae06-4fa0-af2a-448a47da667d` |
| `category_name` | Category name | `Face Painting` |
| `category_description` | Category description | `Traditional face painting services` |

### Visibility Fields
| Field | Description | Values |
|-------|-------------|--------|
| `visible_public` | Show on public booking | `true`/`false` |
| `visible_pos` | Show in POS system | `true`/`false` |
| `visible_events` | Show for events | `true`/`false` |

## How to Edit the CSV

### 1. Adding a New Service with Multiple Tiers

```csv
service_id,service_name,service_description,base_duration,base_price,tier_id,tier_name,tier_duration,tier_price,tier_is_default,tier_sort_order
,Nail Art,Professional nail art services,30,25.00,,Basic,15,15.00,false,1
,Nail Art,Professional nail art services,30,25.00,,Standard,30,25.00,true,2
,Nail Art,Professional nail art services,30,25.00,,Premium,45,40.00,false,3
```

### 2. Adding Tiers to Existing Service

```csv
service_id,service_name,tier_id,tier_name,tier_duration,tier_price,tier_is_default,tier_sort_order
c35117a3-a451-4884-8a29-c930372d8463,Face Painting,,Adult Premium,20,60.00,false,8
c35117a3-a451-4884-8a29-c930372d8463,Face Painting,,Group Package,60,150.00,false,9
```

### 3. Updating Existing Service Details

```csv
service_id,service_name,service_description,base_duration,base_price,service_status
c35117a3-a451-4884-8a29-c930372d8463,Face Painting,Updated description with new details,10,40.00,active
```

## Import Process

### Import Modes

1. **Create Mode**: Only creates new services/tiers, skips existing ones
2. **Update Mode**: Only updates existing services/tiers, skips new ones  
3. **Upsert Mode**: Creates new and updates existing (recommended)

### Step-by-Step Import

1. **Export Current Data**: Download the current services CSV
2. **Edit in Spreadsheet**: Make your changes in Excel/Google Sheets
3. **Save as CSV**: Ensure proper CSV format
4. **Upload**: Use the admin import interface
5. **Review Results**: Check the import summary for any errors

## Best Practices

### Data Preparation
- **Keep service_id**: Don't change existing service IDs
- **Unique tier names**: Each tier within a service should have a unique name
- **Consistent formatting**: Use consistent date/time formats
- **Clean descriptions**: Remove line breaks and special characters

### Validation Tips
- **Required fields**: Ensure all required fields are filled
- **Number formats**: Use decimal format for prices (e.g., 25.00)
- **Boolean values**: Use true/false or 1/0 for boolean fields
- **Status values**: Use only: active, archived, draft

### Common Mistakes to Avoid
- **Mixed service data**: Don't mix different services in the same row
- **Missing base data**: New services need base_duration and base_price
- **Duplicate tier names**: Each tier within a service must have unique name
- **Invalid IDs**: Don't modify existing UUIDs

## Troubleshooting

### Import Errors
- **"Missing required field"**: Check that service_name is provided
- **"Invalid number format"**: Ensure prices/durations are numeric
- **"Service not found"**: Verify service_id exists in database
- **"Duplicate tier name"**: Use unique names within each service

### Data Issues
- **Tiers not appearing**: Check tier_name is not empty or "Base"
- **Wrong default tier**: Only one tier per service should have tier_is_default=true
- **Visibility problems**: Check boolean values are true/false

### Category Issues (Fixed as of 2025-06-21)

- **Missing category_id**: System now auto-resolves category_id from category_name
- **Inconsistent categories**: Database has been cleaned up with proper category assignments
- **Empty category fields**: All services now have valid category relationships

## Support

For additional help with the import/export process:

1. Check the import results summary for specific error messages
2. Validate your CSV structure against the examples above
3. Contact system administrator for complex data migrations

---

**Last updated: 2025-06-21**
