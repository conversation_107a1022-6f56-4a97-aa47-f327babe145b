#!/usr/bin/env node

/**
 * Test script for database export error handling
 * Tests validation errors, invalid parameters, and error responses
 */

const http = require('http');

const BASE_URL = 'http://localhost:3000';

/**
 * Test export with invalid parameters
 */
async function testInvalidFormat() {
  console.log(`\n🧪 Testing invalid format parameter...`);
  
  return new Promise((resolve) => {
    const url = `/api/admin/database/export?format=invalid&tables=all&includeGuide=true`;
    console.log(`📡 Making request to: ${BASE_URL}${url}`);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: url,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DatabaseExportErrorTest/1.0'
      }
    };

    const req = http.request(options, (res) => {
      console.log(`📊 Response status: ${res.statusCode} ${res.statusMessage}`);
      
      let content = '';
      res.on('data', (chunk) => {
        content += chunk;
      });

      res.on('end', () => {
        if (res.statusCode === 400) {
          try {
            const errorData = JSON.parse(content);
            if (errorData.error && errorData.error.includes('Invalid format parameter')) {
              console.log(`✅ Correct error response for invalid format`);
              resolve(true);
            } else {
              console.log(`❌ Unexpected error message: ${errorData.error}`);
              resolve(false);
            }
          } catch (parseError) {
            console.log(`❌ Could not parse error response: ${content}`);
            resolve(false);
          }
        } else {
          console.log(`❌ Expected 400 status code, got ${res.statusCode}`);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Request failed: ${error.message}`);
      resolve(false);
    });

    req.end();
  });
}

/**
 * Test export with invalid tables
 */
async function testInvalidTables() {
  console.log(`\n🧪 Testing invalid tables parameter...`);
  
  return new Promise((resolve) => {
    const url = `/api/admin/database/export?format=json&tables=nonexistent_table&includeGuide=true`;
    console.log(`📡 Making request to: ${BASE_URL}${url}`);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: url,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DatabaseExportErrorTest/1.0'
      }
    };

    const req = http.request(options, (res) => {
      console.log(`📊 Response status: ${res.statusCode} ${res.statusMessage}`);
      
      let content = '';
      res.on('data', (chunk) => {
        content += chunk;
      });

      res.on('end', () => {
        if (res.statusCode === 404) {
          try {
            const errorData = JSON.parse(content);
            if (errorData.error && errorData.error.includes('No data found')) {
              console.log(`✅ Correct error response for invalid tables`);
              resolve(true);
            } else {
              console.log(`❌ Unexpected error message: ${errorData.error}`);
              resolve(false);
            }
          } catch (parseError) {
            console.log(`❌ Could not parse error response: ${content}`);
            resolve(false);
          }
        } else {
          console.log(`❌ Expected 404 status code, got ${res.statusCode}`);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Request failed: ${error.message}`);
      resolve(false);
    });

    req.end();
  });
}

/**
 * Test export with wrong HTTP method
 */
async function testWrongMethod() {
  console.log(`\n🧪 Testing wrong HTTP method...`);
  
  return new Promise((resolve) => {
    const url = `/api/admin/database/export?format=json&tables=all&includeGuide=true`;
    console.log(`📡 Making POST request to: ${BASE_URL}${url}`);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: url,
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DatabaseExportErrorTest/1.0'
      }
    };

    const req = http.request(options, (res) => {
      console.log(`📊 Response status: ${res.statusCode} ${res.statusMessage}`);
      
      let content = '';
      res.on('data', (chunk) => {
        content += chunk;
      });

      res.on('end', () => {
        if (res.statusCode === 405) {
          try {
            const errorData = JSON.parse(content);
            if (errorData.error && errorData.error.includes('Method not allowed')) {
              console.log(`✅ Correct error response for wrong method`);
              resolve(true);
            } else {
              console.log(`❌ Unexpected error message: ${errorData.error}`);
              resolve(false);
            }
          } catch (parseError) {
            console.log(`❌ Could not parse error response: ${content}`);
            resolve(false);
          }
        } else {
          console.log(`❌ Expected 405 status code, got ${res.statusCode}`);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Request failed: ${error.message}`);
      resolve(false);
    });

    req.end();
  });
}

/**
 * Test successful export to verify normal operation
 */
async function testSuccessfulExport() {
  console.log(`\n🧪 Testing successful export (control test)...`);
  
  return new Promise((resolve) => {
    const url = `/api/admin/database/export?format=json&tables=services&includeGuide=true`;
    console.log(`📡 Making request to: ${BASE_URL}${url}`);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: url,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'DatabaseExportErrorTest/1.0'
      }
    };

    const req = http.request(options, (res) => {
      console.log(`📊 Response status: ${res.statusCode} ${res.statusMessage}`);
      
      let content = '';
      res.on('data', (chunk) => {
        content += chunk;
      });

      res.on('end', () => {
        if (res.statusCode === 200) {
          const contentDisposition = res.headers['content-disposition'];
          if (contentDisposition && contentDisposition.includes('attachment')) {
            console.log(`✅ Successful export with proper download headers`);
            resolve(true);
          } else {
            console.log(`❌ Missing download headers in successful response`);
            resolve(false);
          }
        } else {
          console.log(`❌ Expected 200 status code, got ${res.statusCode}`);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Request failed: ${error.message}`);
      resolve(false);
    });

    req.end();
  });
}

/**
 * Main test runner
 */
async function runErrorHandlingTests() {
  console.log(`🚀 Starting Database Export Error Handling Tests`);
  console.log(`📍 Base URL: ${BASE_URL}`);

  const results = {
    invalidFormat: false,
    invalidTables: false,
    wrongMethod: false,
    successfulExport: false
  };

  try {
    // Test invalid format
    results.invalidFormat = await testInvalidFormat();
    
    // Test invalid tables
    results.invalidTables = await testInvalidTables();
    
    // Test wrong HTTP method
    results.wrongMethod = await testWrongMethod();
    
    // Test successful export (control)
    results.successfulExport = await testSuccessfulExport();

    // Summary
    console.log(`\n📊 Error Handling Test Results:`);
    console.log(`   Invalid Format Handling: ${results.invalidFormat ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Invalid Tables Handling: ${results.invalidTables ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Wrong Method Handling: ${results.wrongMethod ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Successful Export (Control): ${results.successfulExport ? '✅ PASS' : '❌ FAIL'}`);

    const allPassed = Object.values(results).every(result => result === true);
    
    if (allPassed) {
      console.log(`\n🎉 All error handling tests passed! Export API handles errors correctly.`);
      process.exit(0);
    } else {
      console.log(`\n⚠️ Some error handling tests failed. Please check the issues above.`);
      process.exit(1);
    }

  } catch (error) {
    console.error(`\n💥 Error handling test runner failed: ${error.message}`);
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runErrorHandlingTests();
}

module.exports = { testInvalidFormat, testInvalidTables, testWrongMethod, testSuccessfulExport };
